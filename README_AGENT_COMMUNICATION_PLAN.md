# Agent Communication Enhancement Plan

## Objective
Enable bidirectional communication between the data retriever agent and data analyst agent, allowing the data retriever to request additional information when needed to properly fetch the required data.

## Current Architecture Analysis

### Current Flow:
1. **Data Analyst Agent** (model.py) → calls `data_retriever` tool
2. **Data Retriever Tool** (tools.py) → calls `run_generic_parser_graph`
3. **Generic Parser Graph** (parser_graph.py) → processes request and returns data
4. **Data returned** to Data Analyst Agent for analysis

### Current State Structures:
- **AgentState** (react_agent_code_2.py): Main agent state with messages, input_data, etc.
- **GenericParserState** (parser_graph.py): Parser-specific state for data retrieval

## Implementation Plan

### Phase 1: State Structure Modifications

#### Task 1.1: Extend GenericParserState
**File**: `parser_graph.py`
**Changes**:
- Add `needed_infos: str` field to GenericParserState class
- This field will contain descriptions of additional information needed by the data retriever

#### Task 1.2: Extend AgentState (if needed)
**File**: `react_agent_code_2.py`
**Changes**:
- Verify if Agent<PERSON><PERSON> needs a `needed_infos` field for communication
- Add field if required for state persistence

### Phase 2: Parser Graph Prompt Modifications

#### Task 2.1: Modify DEFAULT_PROMPT in parser_graph.py
**File**: `parser_graph.py`
**Location**: Lines 35-41 (DEFAULT_PROMPT variable)
**Changes**:
- Add instructions for the agent to include a `needed_infos` field in JSON responses
- Specify that `needed_infos` should contain descriptions of missing information
- Clarify when to use this field (only when necessary data is missing)

**New prompt section to add**:
```
**INFORMATION REQUESTS:**
- If you need additional informations to construct an accurate request, include a "needed_infos" field in your JSON response
- The "needed_infos" field should contain a clear description of what information you need
- Only use this field when essential data is missing and cannot be inferred
- When using "needed_infos", set all other fields to appropriate default/null values
```

#### Task 2.2: Update generate_request function
**File**: `parser_graph.py`
**Location**: Lines 278-339 (generate_request function)
**Changes**:
- Ensure the prompt includes the new needed_infos instructions
- Maintain backward compatibility with existing functionality

### Phase 3: Request Processing Logic

#### Task 3.1: Modify process_generic_parser_request function
**File**: `parser_graph.py`
**Location**: Lines 532-580 (process_generic_parser_request function)
**Changes**:
- Add logic to check for `needed_infos` field in input_json
- When `needed_infos` is present:
  - Skip normal processing (don't force partner code, don't send request)
  - Set all data fields to None/empty
  - Return the needed_infos in the response
- Maintain normal processing when `needed_infos` is not present

#### Task 3.2: Update process_request_node
**File**: `parser_graph.py`
**Location**: Lines 750-810 (process_request_node function)
**Changes**:
- Handle responses containing `needed_infos`
- Update state with needed_infos when present
- Ensure proper state management for information requests

### Phase 4: Tool Communication Enhancement

#### Task 4.1: Enhance data_retriever tool
**File**: `tools.py`
**Location**: Lines 25-84 (data_retriever function)
**Changes**:
- Add logic to handle cases where `needed_infos` is not empty
- When needed_infos is present:
  - Return a special response indicating information is needed
  - Include the needed_infos description in the response
  - Set other fields (input_data, etc.) to empty/None
- Maintain normal data retrieval when needed_infos is empty

**New response structure for information requests**:
```python
{
    "status": "info_needed",
    "needed_infos": response.get("needed_infos", ""),
    "input_data": [],
    "data_description": [],
    "generic_parser_request": [],
    "requests_error": []
}
```

### Phase 5: Data Analyst Agent Prompt Updates

#### Task 5.1: Modify DEFAULT_PROMPT in model.py
**File**: `model.py`
**Location**: Lines 215-315 (DEFAULT_PROMPT variable)
**Changes**:
- Add instructions for handling information requests from data retriever
- Specify how to ask users for additional information
- Provide guidance on re-submitting requests with additional context

**New prompt section to add**:
```
**HANDLING DATA RETRIEVER REQUESTS:**
- If the data retriever indicates it needs additional information (status: "info_needed"), ask the user for the specific information mentioned in "needed_infos"
- Once you receive the additional information from the user, include it in your next data retrieval request
- Be specific about what information is needed and why it's required for data retrieval
- Always provide context about how the additional information will help improve the data retrieval
```

### Phase 6: Integration and Testing

#### Task 6.1: Create test scenarios
**Files**: Create test files or update existing tests
**Changes**:
- Test normal data retrieval (no additional info needed)
- Test information request flow (data retriever needs more info)
- Test complete round-trip communication
- Test error handling and edge cases

#### Task 6.2: Update documentation
**Files**: Update relevant documentation files
**Changes**:
- Document the new communication flow
- Provide examples of the enhanced interaction
- Update API documentation if needed

## Implementation Order

### Priority 1 (Core Functionality):
1. Task 1.1: Extend GenericParserState
2. Task 2.1: Modify DEFAULT_PROMPT in parser_graph.py
3. Task 3.1: Modify process_generic_parser_request function
4. Task 4.1: Enhance data_retriever tool

### Priority 2 (User Experience):
5. Task 5.1: Modify DEFAULT_PROMPT in model.py
6. Task 3.2: Update process_request_node
7. Task 2.2: Update generate_request function

### Priority 3 (Quality Assurance):
8. Task 6.1: Create test scenarios
9. Task 6.2: Update documentation
10. Task 1.2: Extend AgentState (if needed)

## Expected Outcomes

### Before Implementation:
- Data retriever fails or returns incomplete data when information is missing
- No communication channel between agents
- User receives generic error messages

### After Implementation:
- Data retriever can request specific additional information
- Clear communication flow between data retriever and data analyst
- User receives specific, actionable requests for additional information
- Improved data retrieval accuracy and user experience

## Risk Mitigation

### Backward Compatibility:
- All changes maintain existing functionality
- New fields are optional and don't break existing flows
- Gradual rollout possible

### Error Handling:
- Robust handling of malformed needed_infos requests
- Fallback to existing error handling mechanisms
- Clear error messages for debugging

### Performance:
- Minimal impact on existing performance
- Additional logic only executes when needed_infos is present
- No changes to core data processing pipelines

## Success Metrics

1. **Functional**: Successful round-trip communication between agents
2. **User Experience**: Reduced failed data retrieval attempts
3. **Accuracy**: Improved data retrieval success rate
4. **Maintainability**: Clean, documented code with proper error handling
